<?php

namespace Modules\ChatBot\Services;

use Exception;
use Gemini\Data\Content;
use Gemini\Data\GenerationConfig;
use Gemini\Enums\Role;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Events\MessageReceived;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Message;
use Modules\ModelAI\Facades\ModelAIFacade;
use Modules\ModelAI\Models\ModelAI;
use Illuminate\Support\Facades\Log;
use Modules\ModelAI\Models\ModelProvider;
use OpenAI\Laravel\Facades\OpenAI;
use Gemini\Laravel\Facades\Gemini;

class AIService
{

    /**
     * Generate general prompts using AI providers
     * @throws Exception
     */
    public function botGeneralPrompts(): ?string
    {
        // Get the appropriate prompt template
        $prompt = $this->getPromptTemplate();

        $model = $this->getDefaultModel();

        return $this->callAIWithModel($prompt, $model);
    }

    /**
     * Get default model from user preferences or system default
     */
    private function getDefaultModel(): ModelAI
    {
        return ModelAIFacade::getDefaultModel();
    }

    /**
     * Call AI API using ModelAI configuration
     */
    private function callAIWithModel(string $prompt, ModelAI $model): ?string
    {
        $provider = $model->provider;
        try {
            return match ($provider->key) {
                'google', 'gemini' => $this->callGeminiWithLibrary($prompt, $model),
                default => $this->callOpenAIWithLibrary($prompt, $model), // OpenAI library supports multiple providers
            };
        } catch (Exception $e) {
            Log::error('AI Model API Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $provider->key,
                'error' => $e->getMessage(),
                'prompt_length' => strlen($prompt),
            ]);
            return null;
        }
    }

    /**
     * Call Gemini API using google-gemini-php/laravel library
     */
    private function callGeminiWithLibrary(string $prompt, ModelAI $model): ?string
    {
        try {
            $provider = $model->provider;

            $apiKey = $provider->getApiKey();

            if (!$apiKey) {
                throw new Exception('Vui lòng cấu hình API Key cho Gemini trong ModelProvider');
            }

            config()->set(['gemini.api_key' => $apiKey, 'gemini.request_timeout']);
            $gemini = Gemini::generativeModel(model: $model->key)->generateContent($prompt);
            $text = $gemini->text();

            if (empty($text)) {
                throw new Exception('Gemini API trả về response trống.');
            }

            return $text;

        } catch (Exception $e) {
            Log::error('Gemini Library Error', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Call OpenAI API using openai-php/laravel library (supports multiple providers)
     */
    private function callOpenAIWithLibrary(string $prompt, ModelAI $model): ?string
    {
        try {
            // Get API key and base URL from ModelProvider
            $apiKey = $model->provider->getApiKey();
            $baseUrl = $model->provider->base_url;

            if (!$apiKey) {
                throw new Exception("Vui lòng cấu hình API Key cho {$model->provider->name} trong ModelProvider");
            }

            // Create OpenAI client with custom configuration for different providers
            config()->set(['openai.api_key' => $apiKey,
                'openai.base_url' => $baseUrl
            ]);

            // Generate content using chat completions
            $openai = OpenAI::chat()->create([
                'model' => $model->key,
                'messages' => [
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => 2048,
                'temperature' => 0.7,
            ]);

            // Extract text from response
            return $openai->choices[0]->message->content ?? null;

        } catch (Exception $e) {
            Log::error('OpenAI Library Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $model->provider->key,
                'error' => $e->getMessage(),
                'prompt_length' => strlen($prompt),
            ]);
            return null;
        }
    }

    /**
     * Get prompt template based on type
     */
    private function getPromptTemplate(): ?string
    {
        $promptType = request()->input('type');
        return match ($promptType) {
            'system_prompt' => $this->userSystemPrompts(request()->input('role')),
            'greeting_message' => $this->botGreetingMessagePrompt(request()->input('name')),
            'starting_message' => $this->userStartingMessagePrompt(request()->input('system_prompt')),
            default => null,
        };
    }

    private function userSystemPrompts(string $botRole): string
    {
        return "Bạn là một chuyên gia tạo system prompt cho chatbot. Dựa trên vai trò người dùng cung cấp là \"{$botRole}\", hãy tạo ra một system prompt hoàn chỉnh bằng tiếng Việt.
                **QUAN TRỌNG:** Chỉ trả về nội dung của system prompt, không bao gồm bất kỳ lời chào, giải thích, hay định dạng markdown nào khác. Bắt đầu trực tiếp với nội dung prompt.

                Cấu trúc của prompt cần có:
                1.  **Persona:** Mô tả chi tiết vai trò, tính cách.
                2.  **Instructions:** Các quy tắc và hướng dẫn cụ thể.
                3.  **Capabilities:** Liệt kê các việc AI có thể làm.
                4.  **Constraints:** Liệt kê các việc AI không được làm.

                <example>
                Đây là một ví dụ cho vai trò \"Trợ lý hỗ trợ thủ tục hành chính\":

                Bạn là một Trợ lý ảo chuyên nghiệp, chuyên về lĩnh vực thủ tục hành chính tại Việt Nam. Phong thái làm việc của bạn là chính xác, đáng tin cậy, và luôn kiên nhẫn hướng dẫn người dùng. Giọng văn của bạn cần rõ ràng, trang trọng nhưng phải dễ hiểu đối với người dân, tránh dùng các thuật ngữ pháp lý quá phức tạp. Mục tiêu của bạn là giúp người dùng tiết kiệm thời gian và công sức khi thực hiện các thủ tục với cơ quan nhà nước.

                **Instructions:**
                * Luôn trả lời một cách chính xác, dựa trên các văn bản pháp luật và nguồn tin chính thống.
                * Ưu tiên thông tin từ Cổng Dịch vụ công Quốc gia, website của các Bộ, ngành và chính quyền địa phương.
                * Khi giải thích quy định, hãy cố gắng diễn giải lại bằng ngôn ngữ đơn giản, dễ hiểu.
                * Nếu câu hỏi của người dùng không đủ rõ, hãy chủ động đặt câu hỏi để làm rõ thông tin trước khi trả lời.
                * Nếu không tìm thấy thông tin, phải thẳng thắn thừa nhận và gợi ý người dùng liên hệ cơ quan có thẩm quyền.

                **Capabilities:**
                * Giải thích quy trình, các bước thực hiện một thủ tục hành chính.
                * Liệt kê các loại hồ sơ, giấy tờ cần chuẩn bị, bao gồm số lượng và yêu cầu (nếu có).
                * Xác định cơ quan nhà nước có thẩm quyền tiếp nhận và giải quyết hồ sơ.
                * Tra cứu và cung cấp thông tin về các loại phí, lệ phí liên quan.
                * Cung cấp liên kết đến các văn bản pháp luật gốc (Luật, Nghị định, Thông tư).

                **Constraints:**
                * **TUYỆT ĐỐI KHÔNG** đưa ra lời khuyên pháp lý (legal advice).
                * Không đưa ra ý kiến, đánh giá cá nhân về tính hợp lý của các thủ tục.
                * Không tự ý suy diễn hay bịa đặt thông tin không có trong văn bản pháp luật.
                * Không xử lý, yêu cầu hay lưu trữ các thông tin cá nhân nhạy cảm của người dùng.
                </example>
                ";
    }


    private function botGreetingMessagePrompt(string $name): string
    {
        return "Viết một câu chào mừng ngắn gọn, thân thiện (dưới 15 từ) bằng tiếng Việt cho một chatbot tên là \"{$name}\". Chỉ trả về duy nhất câu chào.";
    }

    private function userStartingMessagePrompt(string $systemPrompt): string
    {
        return "Dựa vào prompt hệ thống sau của một chatbot: \"{$systemPrompt}\". Hãy đề xuất 4 câu hỏi ngắn gọn mà người dùng có thể hỏi. Yêu cầu: Chỉ trả về một mảng JSON chứa các chuỗi ký tự. Ví dụ: [\"Học phí bao nhiêu?\", \"Trường có những ngành nào?\"]";
    }

    public function getAssistantPrompts(Bot $bot, Message $question, Message $assistant, $messages, string $enhancedUserMessage = null): ?bool
    {
        $provider = $bot->aiModel->provider;
        $model = $bot->aiModel;
        $systemPrompt = $bot->system_prompt;

        // Get secure parameters from bot (ModelService → Bot → Secure)
        $secureParams = $bot->getSecureAIParameters();

        Log::info('Question Get AssistantPrompts:::---->::::', $question->toArray());

        try {
            return match ($provider->key) {
                'google', 'gemini' => $this->getGoogleProviderCallback($question, $assistant, $systemPrompt, $provider, $model, $messages, $secureParams, $enhancedUserMessage),
                default => $this->getOpenAIProviderCallback($question, $assistant, $systemPrompt, $provider, $model, $messages, $secureParams, $enhancedUserMessage)
            };

        } catch (Exception $e) {
            Log::error('AI Model API Error', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $provider->key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function getGoogleProviderCallback(Message $question, Message $assistant, string $systemPrompt, ModelProvider $provider, ModelAI $model, $messages, array $secureParams, string $enhancedUserMessage = null): bool
    {
        try {
            // Use getApiKey() method for consistency
            $apiKey = $provider->getApiKey();
            if (!$apiKey) {
                return false;
            }

            $systemPrompt = [
                Content::parse($systemPrompt, role: Role::USER)
            ];

            $history = $messages
                ->filter(function ($msg) {
                    return !empty($msg->content);
                })
                ->map(function ($msg) use ($enhancedUserMessage, $question) {
                    // Use enhanced message for the specific question, otherwise use original content
                    $contentToUse = ($msg->uuid == $question->uuid && !empty($enhancedUserMessage))
                        ? $enhancedUserMessage
                        : $msg->content;

                    // Ensure content is properly encoded
                    $contentToUse = mb_convert_encoding($contentToUse, 'UTF-8', 'UTF-8');

                    Log::info('Content being used for AI', [
                        'message_uuid' => $msg->uuid,
                        'is_enhanced' => $msg->uuid == $question->uuid && !empty($enhancedUserMessage),
                        'content_length' => mb_strlen($contentToUse),
                        'encoding_valid' => mb_check_encoding($contentToUse, 'UTF-8')
                    ]);

                    return Content::parse($contentToUse, $msg->role == MessageRole::USER ? Role::USER : Role::MODEL);
                })->toArray();

            $contents = array_merge($systemPrompt, $history);

            // Set config with validated API key
            config()->set(['gemini.api_key' => $apiKey]);

            // Verify config was set correctly
            $configApiKey = config('gemini.api_key');
            if ($configApiKey !== $apiKey) {
                return false;
            }

            // Log final generation config
            Log::info('🔧 GEMINI GENERATION CONFIG', [
                'contents' => $contents
            ]);

            $camelCase = useCamelCase($secureParams);
            if (isset($camelCase['maxTokens'])) {
                $camelCase['maxOutputTokens'] = $camelCase['maxTokens'];
                unset($camelCase['maxTokens']);
            }

            $gemini = Gemini::generativeModel($model->key)
                ->withGenerationConfig(new GenerationConfig(...$camelCase))
                ->generateContent(...$contents);

            if (empty($gemini->text())) {
                return false;
            }

            $response = $gemini->toArray();
            $usage = $response['usageMetadata'] ?? [];

            $question->update([
                'prompt_tokens' => $usage['promptTokenCount'] ?? 0,
                'status' => 'completed'
            ]);

            $assistant->update([
                'content' => $gemini->text(),
                'status' => 'completed',
                'prompt_tokens' => $usage['promptTokenCount'] ?? 0,
                'completion_tokens' => $usage['candidatesTokenCount'] ?? 0,
                'total_tokens' => $usage['totalTokenCount'] ?? 0
            ]);

            broadcast(new MessageReceived($assistant));

            return true;

        } catch (Exception $e) {
            Log::error('Gemini Chat Provider Error', [
                'model_id' => $model->id,
                'provider_id' => $provider->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    private function getOpenAIProviderCallback(Message $question, Message $assistant, string $systemPrompt, ModelProvider $provider, ModelAI $model, $messages, array $secureParams, string $enhancedUserMessage = null): bool
    {
        try {
            // Get API key and base URL from ModelProvider
            $apiKey = $provider->getApiKey();
            $baseUrl = $provider->base_url;

            if (!$apiKey) {
                throw new Exception("Vui lòng cấu hình API Key cho {$provider->name} trong ModelProvider");
            }

            // Create OpenAI client with custom configuration
            config()->set(['openai.api_key' => $apiKey, 'openai.base_url' => $baseUrl]);

            // Build messages array
            $chatMessages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            foreach ($messages as $msg) {
                // Skip messages with null or empty content
                if (empty($msg->content)) {
                    continue;
                }

                // Use enhanced message for the specific question, otherwise use original content
                $contentToUse = ($msg->uuid == $question->uuid && !empty($enhancedUserMessage))
                    ? $enhancedUserMessage
                    : $msg->content;

                // Ensure content is properly encoded
                $contentToUse = mb_convert_encoding($contentToUse, 'UTF-8', 'UTF-8');

                $chatMessages[] = [
                    'role' => $msg->role == MessageRole::USER ? 'user' : 'assistant',
                    'content' => $contentToUse
                ];
            }

            // Add current question to messages (use enhanced if available)
            $questionContent = !empty($enhancedUserMessage) ? $enhancedUserMessage : $question->content;
            $questionContent = mb_convert_encoding($questionContent, 'UTF-8', 'UTF-8');

            $chatMessages[] = [
                'role' => 'user',
                'content' => $questionContent
            ];

            Log::info('OpenAI content being used', [
                'question_uuid' => $question->uuid,
                'is_enhanced' => !empty($enhancedUserMessage),
                'content_length' => mb_strlen($questionContent),
                'encoding_valid' => mb_check_encoding($questionContent, 'UTF-8')
            ]);

            // Build request parameters with secure params
            $requestParams = [
                'model' => $model->key,
                'messages' => $chatMessages,
            ];

            // Log complete request data being sent to OpenAI
            Log::info('🚀 OPENAI REQUEST - COMPLETE DATA', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'provider' => $provider->name,
                'base_url' => $baseUrl,
                'question_content' => $question->content,
                'question_content_length' => strlen($question->content ?? ''),
                'question_role' => $question->role->value ?? 'unknown',
                'system_prompt' => $systemPrompt,
                'system_prompt_length' => strlen($systemPrompt),
                'history_messages_count' => $messages->count(),
                'history_messages' => $messages->map(function ($msg) {
                    return [
                        'id' => $msg->id,
                        'role' => $msg->role->value ?? 'unknown',
                        'content' => $msg->content,
                        'content_length' => strlen($msg->content ?? ''),
                        'created_at' => $msg->created_at?->toISOString(),
                    ];
                })->toArray(),
                'chat_messages_count' => count($chatMessages),
                'chat_messages_structure' => array_map(function ($msg, $index) {
                    return [
                        'index' => $index,
                        'role' => $msg['role'],
                        'content_length' => strlen($msg['content'] ?? ''),
                        'content_preview' => substr($msg['content'] ?? '', 0, 200) . (strlen($msg['content'] ?? '') > 200 ? '...' : ''),
                    ];
                }, $chatMessages, array_keys($chatMessages)),
                'total_input_length' => array_sum(array_map(fn($msg) => strlen($msg['content'] ?? ''), $chatMessages)),
                'secure_params' => $secureParams,
                'timestamp' => now()->toISOString(),
            ]);

            // Apply secure parameters
            if (isset($secureParams['temperature'])) {
                $requestParams['temperature'] = $secureParams['temperature'];
            }
            if (isset($secureParams['max_tokens'])) {
                $requestParams['max_tokens'] = $secureParams['max_tokens'];
            }
            if (isset($secureParams['top_p'])) {
                $requestParams['top_p'] = $secureParams['top_p'];
            }
            if (isset($secureParams['frequency_penalty'])) {
                $requestParams['frequency_penalty'] = $secureParams['frequency_penalty'];
            }
            if (isset($secureParams['presence_penalty'])) {
                $requestParams['presence_penalty'] = $secureParams['presence_penalty'];
            }

            // Log final request parameters
            Log::info('🔧 OPENAI REQUEST PARAMS', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'request_params' => array_merge($requestParams, [
                    'messages' => array_map(function ($msg) {
                        return [
                            'role' => $msg['role'],
                            'content_length' => strlen($msg['content'] ?? ''),
                            'content_preview' => substr($msg['content'] ?? '', 0, 100) . '...',
                        ];
                    }, $requestParams['messages'])
                ]),
                'total_messages_count' => count($requestParams['messages']),
                'total_input_length' => array_sum(array_map(fn($msg) => strlen($msg['content'] ?? ''), $requestParams['messages'])),
            ]);

            // Generate content using chat completions
            $openai = OpenAI::chat()->create($requestParams);

            $content = $openai->choices[0]->message->content ?? null;
            if (empty($content)) {
                Log::warning('Empty response from OpenAI API', [
                    'model_id' => $model->id,
                    'model_key' => $model->key,
                    'openai_response' => $openai->toArray(),
                ]);
                return false;
            }

            // Update messages with response and usage
            $usage = $openai->usage ?? null;

            // Log complete response from OpenAI
            Log::info('✅ OPENAI RESPONSE - COMPLETE DATA', [
                'model_id' => $model->id,
                'model_key' => $model->key,
                'response_content' => $content,
                'response_content_length' => strlen($content ?? ''),
                'usage' => $usage ? [
                    'prompt_tokens' => $usage->promptTokens ?? 0,
                    'completion_tokens' => $usage->completionTokens ?? 0,
                    'total_tokens' => $usage->totalTokens ?? 0,
                ] : null,
                'choices_count' => count($openai->choices ?? []),
                'full_openai_response' => $openai->toArray(),
                'timestamp' => now()->toISOString(),
            ]);

            // Log before updating question message
            Log::info('🔄 BEFORE UPDATING QUESTION MESSAGE (OpenAI)', [
                'question_id' => $question->id,
                'question_content_before' => $question->content,
                'question_content_length_before' => strlen($question->content ?? ''),
                'question_dirty_attributes' => $question->getDirty(),
                'question_original_attributes' => $question->getOriginal(),
                'is_content_dirty' => $question->isDirty('content'),
                'prompt_tokens_to_update' => $usage->promptTokens ?? 0,
            ]);

            $question->update([
                'prompt_tokens' => $usage->promptTokens ?? 0,
                'status' => 'completed'
            ]);

            // Log after updating question message
            Log::info('🔄 AFTER UPDATING QUESTION MESSAGE (OpenAI)', [
                'question_id' => $question->id,
                'question_content_after' => $question->content,
                'question_content_length_after' => strlen($question->content ?? ''),
                'question_status' => $question->status,
                'question_prompt_tokens' => $question->prompt_tokens,
                'content_changed_during_update' => $question->wasChanged('content'),
            ]);

            $assistant->update([
                'content' => $content,
                'status' => 'completed',
                'prompt_tokens' => $usage->promptTokens ?? 0,
                'completion_tokens' => $usage->completionTokens ?? 0,
                'total_tokens' => $usage->totalTokens ?? 0
            ]);

            broadcast(new MessageReceived($assistant));

            return true;

        } catch (Exception $e) {
            Log::error('OpenAI Provider Error', [
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

}
