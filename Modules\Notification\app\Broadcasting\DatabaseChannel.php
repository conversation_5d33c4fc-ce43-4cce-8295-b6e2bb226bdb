<?php

namespace Modules\Notification\Broadcasting;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Notification\Models\Notification;
use Modules\User\Models\User;
use Throwable;

class DatabaseChannel
{
    /**
     * Send the given notification.
     * @throws Throwable
     */
    public function send(array $recipient, array $content, array $data): void
    {
        logger()->info("Sending notification to database", $recipient);
        try {
            $notificationTypeId = $data['notification_type_id'] ?? 1;

            // Kiểm tra notifiable_id không được null
            $notifiableId = $recipient['id'] ?? null;
            if ($notifiableId === null) {
                logger()->warning("Database notification skipped: notifiable_id is null", [
                    'recipient' => $recipient,
                    'content' => $content,
                    'data' => $data
                ]);
                return;
            }

            // Tạo notification record với custom table structure
            $notificationData = [
                'notification_type_id' => $notificationTypeId,
                'notifiable_type' => User::class,
                'notifiable_id' => $notifiableId,
                'channel' => 'database',
                'title' => $content['title'] ?? $content['subject'] ?? 'Notification',
                'content' => $content['content'] ?? '',
                'data' => json_encode([
                    'subject' => $content['subject'] ?? '',
                    'original_data' => $data
                ]),
                'metadata' => json_encode($data),
                'priority' => 'normal',
                'status' => 'sent',
                'sent_at' => now()
            ];

            Notification::create($notificationData);

        } catch (Throwable $e) {
            logger()->error("Database notification failed", [
                'error' => $e->getMessage(),
                'notifiable' => $recipient,
                'content' => $content
            ]);

            throw $e;
        }
    }
}
