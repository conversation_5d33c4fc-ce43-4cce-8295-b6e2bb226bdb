<?php

require_once __DIR__ . '/vendor/autoload.php';

use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Events\QueryResultsReceived;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Debug QueryResultsReceived Broadcasting ===\n\n";

try {
    // Get a recent query to test
    $query = Query::with(['conversation'])->latest()->first();

    if (!$query) {
        echo "❌ No queries found in database\n";
        echo "Creating a test query...\n\n";

        // Create a test query
        $query = new Query([
            'uuid' => \Illuminate\Support\Str::uuid(),
            'question' => 'Test question for broadcasting',
            'status' => 'completed', // Set to completed for testing
            'owner_id' => 1,
            'bot_id' => 1,
            'conversation_id' => null,
            'results' => [
                ['title' => 'Test Result 1', 'content' => 'Test content 1'],
                ['title' => 'Test Result 2', 'content' => 'Test content 2']
            ],
            'metadata' => ['test' => true]
        ]);

        // Don't save to DB, just use for testing
        echo "✅ Test query created\n\n";
    }
    
    echo "📋 Query Information:\n";
    echo "- ID: {$query->id}\n";
    echo "- UUID: {$query->uuid}\n";
    echo "- Status: {$query->status}\n";
    echo "- Owner ID: {$query->owner_id}\n";
    echo "- Conversation ID: {$query->conversation_id}\n";
    echo "- Results count: " . (is_array($query->results) ? count($query->results) : 'null/empty') . "\n";
    echo "- Conversation loaded: " . ($query->conversation ? 'Yes (UUID: ' . $query->conversation->uuid . ')' : 'No') . "\n\n";
    
    // Test shouldBroadcast conditions
    echo "🔍 shouldBroadcast() Conditions:\n";
    $statusCheck = $query->status === 'completed';
    $resultsCheck = !empty($query->results);
    
    echo "- Status is 'completed': " . ($statusCheck ? '✅ Yes' : '❌ No (current: ' . $query->status . ')') . "\n";
    echo "- Results not empty: " . ($resultsCheck ? '✅ Yes' : '❌ No') . "\n";
    echo "- Overall shouldBroadcast: " . (($statusCheck && $resultsCheck) ? '✅ Yes' : '❌ No') . "\n\n";
    
    // Test broadcast channels
    echo "📡 Broadcast Channels:\n";
    $broadcastData = [
        'query_id' => $query->uuid,
        'question' => $query->question,
        'results' => $query->getFormattedResults(),
        'timestamp' => now()->toISOString(),
        'status' => $query->status,
    ];
    
    $event = new QueryResultsReceived($query, $broadcastData);
    $channels = $event->broadcastOn();
    
    if (empty($channels)) {
        echo "❌ No broadcast channels configured\n";
    } else {
        foreach ($channels as $channel) {
            echo "- Channel: {$channel->name}\n";
        }
    }
    
    echo "\n🎯 Broadcasting Configuration:\n";
    echo "- BROADCAST_CONNECTION: " . config('broadcasting.default') . "\n";
    echo "- Chatbot broadcasting enabled: " . (config('chatbot.broadcasting.enabled') ? 'Yes' : 'No') . "\n";
    
    // Test actual broadcast
    echo "\n🚀 Testing Broadcast:\n";
    if ($statusCheck && $resultsCheck) {
        echo "Attempting to fire QueryResultsReceived event...\n";
        event($event);
        echo "✅ Event fired successfully\n";
    } else {
        echo "❌ Cannot broadcast - conditions not met\n";
        
        if (!$statusCheck) {
            echo "💡 Fix: Query status needs to be 'completed'\n";
        }
        if (!$resultsCheck) {
            echo "💡 Fix: Query needs to have results\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== Debug Complete ===\n";
