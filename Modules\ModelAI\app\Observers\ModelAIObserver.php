<?php

namespace Modules\ModelAI\Observers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\ModelAI\Models\ModelAI;

class ModelAIObserver
{
    private const CACHE_TAGS = ['model-ai'];

    public function creating(ModelAI $modelAI): void
    {
        $this->handleDefaultState($modelAI);
    }

    public function created(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    public function updating(ModelAI $modelAI): void
    {
        if ($modelAI->isDirty('is_default')) {
            $this->handleDefaultState($modelAI);
        }
    }

    public function updated(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    public function deleted(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    public function restored(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    public function forceDeleted(ModelAI $modelAI): void
    {
        $this->clearCache();
    }

    private function handleDefaultState(ModelAI $modelAI): void
    {
        if ($modelAI->is_default) {
            ModelAI::where($modelAI->getKeyName(), '!=', $modelAI->getKey())
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }
    }

    private function clearCache(): void
    {
        try {
            Cache::tags(self::CACHE_TAGS)->flush();
        } catch (\Exception $e) {
            Log::error('Failed to clear ModelAI cache: ' . $e->getMessage());
        }
    }
}
