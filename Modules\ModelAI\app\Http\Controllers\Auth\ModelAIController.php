<?php

namespace Modules\ModelAI\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\ModelAI\Http\Filters\ModelAIFilter;
use Modules\ModelAI\Http\Requests\BulkModelAIRequest;
use Modules\ModelAI\Http\Requests\BulkModelAIDestroyRequest;
use Modules\ModelAI\Http\Requests\ModelAIRequest;
use Modules\ModelAI\Http\Requests\ModelServiceRequest;
use Modules\ModelAI\Models\ModelAI;
use Modules\ModelAI\Models\ModelService;
use Throwable;

class ModelAIController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|model-ai.view')->only(['index', 'show', 'dropdown', 'byProvider']);
        $this->middleware('role_or_permission:super-admin|model-ai.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|model-ai.edit')->only(['update', 'setDefault']);
        $this->middleware('role_or_permission:super-admin|model-ai.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|model-ai.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $models = ModelAI::query()
            ->with([
                'provider:id,name,key',
                'categories:id,name,key',
                'service'
            ])
            ->filter(new ModelAIFilter($request))
            ->ordered()
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($models, __('AI models retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     * @throws Throwable
     */
    public function store(ModelAIRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $model = ModelAI::create($request->except('categories'));
            if ($request->has('categories')) {
                $model->categories()->sync($request->categories);
            }

            $model->load(['provider:id,name,key', 'categories:id,name,key', 'services']);

            DB::commit();

            return $this->successResponse($model, __('AI model created successfully.'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->safeErrorResponse($e, __('Failed to create AI model.'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $model = ModelAI::withTrashed()->findOrFail($id);
        $model->load(['provider', 'categories', 'services']);
        return $this->successResponse($model, __('AI model retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     * @throws Throwable
     */
    public function update(ModelAIRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $model = ModelAI::withTrashed()->findOrFail($id);
            $model->update($request->except('categories'));

            if ($request->has('categories')) {
                $model->categories()->sync($request->categories);
            }

            DB::commit();

            return $this->successResponse($model->fresh(), __('AI model updated successfully.'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->safeErrorResponse($e, __('Failed to update AI model.'));
        }
    }

    /**
     * Soft delete the specified resource.
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $model = ModelAI::withTrashed()->findOrFail($id);

            // Prevent deletion of default model
            if ($model->is_default) {
                return $this->errorResponse(
                    null,
                    __('Cannot delete the default AI model.'),
                    422
                );
            }

            $model->delete();

            return $this->successResponse(null, __('AI model deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to delete AI model.'));
        }
    }

    /**
     * Restore the specified resource from trash.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $model = ModelAI::onlyTrashed()->findOrFail($id);
            $model->restore();

            return $this->successResponse($model->fresh(), __('AI model restored successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to restore AI model.'));
        }
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $model = ModelAI::withTrashed()->findOrFail($id);
            $model->forceDelete();

            return $this->successResponse(null, __('AI model permanently deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to permanently delete AI model.'));
        }
    }

    /**
     * Bulk soft delete models.
     */
    public function bulkDelete(BulkModelAIRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $models = ModelAI::whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($models as $model) {
            if (!$model->is_default) {
                $model->delete();
                $deletedCount++;
            } else {
                $errors[] = "Model '{$model->name}' cannot be deleted (default model).";
            }
        }

        $message = __(':count models deleted successfully.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some models could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk restore models.
     */
    public function bulkRestore(BulkModelAIRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $restoredCount = ModelAI::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __('Models restored successfully.')
        );
    }

    /**
     * Bulk permanently delete models.
     */
    public function bulkDestroy(BulkModelAIDestroyRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $models = ModelAI::withTrashed()->whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($models as $model) {
            try {
                $model->forceDelete();
                $deletedCount++;
            } catch (\Exception $e) {
                $errors[] = "Model '{$model->name}' cannot be permanently deleted.";
            }
        }

        $message = __(':count models permanently deleted.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some models could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Get models for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $models = ModelAI::query()
            ->with(['provider:id,name,key'])
            ->active()
            ->orderBy('name')
            ->get()
            ->map(function (ModelAI $modelAI) {
                return [
                    'key' => $modelAI->key,
                    'name' => $modelAI->name,
                    'description' => $modelAI->description
                ];
            });
        return $this->successResponse(
            $models,
            __('Models retrieved successfully.')
        );
    }

    /**
     * Get models by provider.
     */
    public function byProvider(int $providerId): JsonResponse
    {
        $models = ModelAI::query()
            ->with(['provider', 'categories'])
            ->active()
            ->provider($providerId)
            ->ordered()
            ->get();

        return $this->successResponse($models, __('AI models retrieved successfully.'));
    }

    /**
     * Set model as default.
     */
    public function setDefault(ModelAI $modelAi): JsonResponse
    {
        $modelAi->update(['is_default' => true]);

        return $this->successResponse($modelAi, __('AI model set as default successfully.'));
    }

    /**
     * Update Or Create Service.
     */
    public function updateOrCreateService(ModelServiceRequest $request): JsonResponse
    {
        try {
            $service = ModelService::query()->updateOrCreate([
                'id' => $request->input('id'),
                'model_ai_id' => $request->input('model_ai_id')
            ], $request->all());

            return $this->successResponse($service, __('Service updated or created successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e);
        }
    }
}
